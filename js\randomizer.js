// Reasons I Love You - Randomizer Functionality
const loveReasons = [
    "Your smile lights up my entire world and makes even the darkest days feel bright",
    "The way you laugh at my silly jokes, even when they're not that funny",
    "How you always know exactly what to say to make me feel better",
    "Your kindness towards everyone you meet, it shows your beautiful heart",
    "The way you scrunch your nose when you're concentrating on something",
    "How you make ordinary moments feel like magical adventures",
    "Your incredible strength and how you face challenges with such grace",
    "The way you listen to me with your whole heart, not just your ears",
    "How you remember the little things that matter to me",
    "Your amazing ability to make me feel loved and appreciated every single day",
    "The way you dance when you think no one is watching",
    "How you always encourage me to be the best version of myself",
    "Your beautiful eyes that tell a thousand stories without saying a word",
    "The way you make me feel like I'm home, no matter where we are",
    "How you turn simple moments into treasured memories",
    "Your incredible sense of humor that never fails to make me smile",
    "The way you care for others shows the depth of your compassion",
    "How you make me want to be a better person just by being yourself",
    "Your unique perspective on life that always amazes me",
    "The way you make me feel like the luckiest person in the world",
    "How you bring out the best in everyone around you",
    "Your determination and passion for the things you believe in",
    "The way you make even grocery shopping feel like a fun adventure",
    "How you always find the silver lining in any situation",
    "Your incredible talent for making people feel special and valued",
    "The way you support my dreams and believe in me unconditionally",
    "How you make me laugh until my stomach hurts",
    "Your beautiful soul that shines through everything you do",
    "The way you make me feel understood and accepted completely",
    "How you turn ordinary days into extraordinary memories just by being there"
];

class LoveRandomizer {
    constructor() {
        this.currentReason = null;
        this.favorites = this.loadFavorites();
        this.usedReasons = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.displayFavorites();
    }

    initializeElements() {
        this.reasonText = document.getElementById('reason-text');
        this.heartBtn = document.getElementById('heart-btn');
        this.newReasonBtn = document.getElementById('new-reason-btn');
        this.favoritesContainer = document.getElementById('favorites-container');
        this.favoritesGrid = document.getElementById('favorites-grid');
        this.clearFavoritesBtn = document.getElementById('clear-favorites');
    }

    setupEventListeners() {
        this.newReasonBtn.addEventListener('click', () => this.showNewReason());
        this.heartBtn.addEventListener('click', () => this.toggleFavorite());
        this.clearFavoritesBtn.addEventListener('click', () => this.clearAllFavorites());
    }

    showNewReason() {
        // Add loading state
        this.newReasonBtn.classList.add('loading');
        this.newReasonBtn.innerHTML = '🎲 Finding reason<span class="spinner"></span>';
        
        setTimeout(() => {
            const reason = this.getRandomReason();
            this.currentReason = reason;
            
            // Animate reason change
            this.reasonText.style.opacity = '0';
            this.reasonText.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                this.reasonText.textContent = reason;
                this.reasonText.style.opacity = '1';
                this.reasonText.style.transform = 'translateY(0)';
                this.updateHeartButton();
                
                // Remove loading state
                this.newReasonBtn.classList.remove('loading');
                this.newReasonBtn.innerHTML = '🎲 New Reason';
            }, 200);
        }, 500);
    }

    getRandomReason() {
        // If we've used all reasons, reset the used array
        if (this.usedReasons.length >= loveReasons.length) {
            this.usedReasons = [];
        }
        
        // Get available reasons
        const availableReasons = loveReasons.filter((reason, index) => 
            !this.usedReasons.includes(index)
        );
        
        // Pick a random available reason
        const randomIndex = Math.floor(Math.random() * availableReasons.length);
        const selectedReason = availableReasons[randomIndex];
        
        // Mark this reason as used
        const originalIndex = loveReasons.indexOf(selectedReason);
        this.usedReasons.push(originalIndex);
        
        return selectedReason;
    }

    updateHeartButton() {
        if (this.currentReason && this.favorites.includes(this.currentReason)) {
            this.heartBtn.textContent = '❤️';
            this.heartBtn.classList.add('favorited');
            this.heartBtn.title = 'Remove from favorites';
        } else {
            this.heartBtn.textContent = '🤍';
            this.heartBtn.classList.remove('favorited');
            this.heartBtn.title = 'Save to favorites';
        }
    }

    toggleFavorite() {
        if (!this.currentReason) return;
        
        const index = this.favorites.indexOf(this.currentReason);
        
        if (index > -1) {
            // Remove from favorites
            this.favorites.splice(index, 1);
            this.heartBtn.style.animation = 'heartBreak 0.3s ease';
        } else {
            // Add to favorites
            this.favorites.push(this.currentReason);
            this.heartBtn.style.animation = 'heartBeat 0.3s ease';
        }
        
        this.saveFavorites();
        this.updateHeartButton();
        this.displayFavorites();
        
        // Reset animation
        setTimeout(() => {
            this.heartBtn.style.animation = '';
        }, 300);
    }

    loadFavorites() {
        const saved = localStorage.getItem('love-favorites');
        return saved ? JSON.parse(saved) : [];
    }

    saveFavorites() {
        localStorage.setItem('love-favorites', JSON.stringify(this.favorites));
    }

    displayFavorites() {
        if (this.favorites.length === 0) {
            this.favoritesContainer.innerHTML = `
                <p style="color: var(--gray); font-style: italic; text-align: center;">
                    No favorites saved yet. Click the heart on reasons you love! 💕
                </p>
            `;
            this.favoritesGrid.innerHTML = '';
            this.clearFavoritesBtn.style.display = 'none';
        } else {
            this.favoritesContainer.innerHTML = `
                <p style="color: var(--dark-pink); text-align: center; margin-bottom: 1rem;">
                    You have ${this.favorites.length} favorite reason${this.favorites.length !== 1 ? 's' : ''} saved! 💖
                </p>
            `;
            
            this.favoritesGrid.innerHTML = '';
            this.favorites.forEach((reason, index) => {
                const favoriteItem = document.createElement('div');
                favoriteItem.className = 'favorite-item';
                favoriteItem.innerHTML = `
                    <button class="remove-favorite" data-index="${index}" title="Remove from favorites">×</button>
                    <div>${reason}</div>
                `;
                
                // Add remove functionality
                const removeBtn = favoriteItem.querySelector('.remove-favorite');
                removeBtn.addEventListener('click', () => this.removeFavorite(index));
                
                this.favoritesGrid.appendChild(favoriteItem);
            });
            
            this.clearFavoritesBtn.style.display = 'inline-block';
        }
    }

    removeFavorite(index) {
        this.favorites.splice(index, 1);
        this.saveFavorites();
        this.displayFavorites();
        this.updateHeartButton();
    }

    clearAllFavorites() {
        if (confirm('Are you sure you want to clear all your favorite reasons? This cannot be undone.')) {
            this.favorites = [];
            this.saveFavorites();
            this.displayFavorites();
            this.updateHeartButton();
        }
    }
}

// Initialize randomizer when page loads
document.addEventListener('DOMContentLoaded', function() {
    const randomizer = new LoveRandomizer();
    
    // Add heart animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes heartBeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }
        
        @keyframes heartBreak {
            0% { transform: scale(1); }
            25% { transform: scale(0.8) rotate(-10deg); }
            50% { transform: scale(0.9) rotate(10deg); }
            75% { transform: scale(0.85) rotate(-5deg); }
            100% { transform: scale(1) rotate(0deg); }
        }
        
        .mr-2 {
            margin-right: 1rem;
        }
    `;
    document.head.appendChild(style);
});
