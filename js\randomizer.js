// Reasons I Love You - Randomizer Functionality
const loveReasons = [
    "The way your voice makes everything better, even through a phone screen",
    "How you send me good morning texts that instantly make my day brighter",
    "Your laugh - I could listen to it on repeat and never get tired of it",
    "How you remember every little detail I tell you, even the random stuff",
    "The way you make me feel like I'm the most important person in your world",
    "How our late-night video calls feel like the best part of my day",
    "Your incredible ability to make me smile even when I'm having the worst day",
    "How you listen to me ramble about random things and actually seem interested",
    "The way you get excited about the little things - it's absolutely adorable",
    "How you make me feel understood in ways I never thought possible",
    "Your texts that come at exactly the right moment, like you can read my mind",
    "How you make distance feel like nothing when we're talking",
    "The way you support my dreams even though we're miles apart",
    "How you make me laugh until I'm crying happy tears",
    "Your kindness that shines through every message you send",
    "How you make ordinary conversations feel like the highlight of my day",
    "The way you remember things I mentioned weeks ago - it means everything",
    "How you make me feel less alone, even when we're in different places",
    "Your amazing ability to turn my bad moods into good ones",
    "How you make me excited about the future, especially meeting you someday",
    "The way you care about my family and friends like they're your own",
    "How you make me feel brave enough to be completely myself with you",
    "Your sweet voice messages that I save and listen to when I miss you",
    "How you make me believe in love in ways I never did before",
    "The way you make me want to be better, not because I have to, but because I want to",
    "How you turn my random thoughts into deep conversations",
    "Your patience with me when I'm being difficult or moody",
    "How you make me feel like home is wherever you are, even if it's far away",
    "The way you make me count down days until we can finally be together",
    "How you love me for exactly who I am, flaws and all"
];

class LoveRandomizer {
    constructor() {
        this.currentReason = null;
        this.favorites = this.loadFavorites();
        this.usedReasons = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.displayFavorites();
    }

    initializeElements() {
        this.reasonText = document.getElementById('reason-text');
        this.heartBtn = document.getElementById('heart-btn');
        this.newReasonBtn = document.getElementById('new-reason-btn');
        this.favoritesContainer = document.getElementById('favorites-container');
        this.favoritesGrid = document.getElementById('favorites-grid');
        this.clearFavoritesBtn = document.getElementById('clear-favorites');
    }

    setupEventListeners() {
        this.newReasonBtn.addEventListener('click', () => this.showNewReason());
        this.heartBtn.addEventListener('click', () => this.toggleFavorite());
        this.clearFavoritesBtn.addEventListener('click', () => this.clearAllFavorites());
    }

    showNewReason() {
        // Add loading state
        this.newReasonBtn.classList.add('loading');
        this.newReasonBtn.innerHTML = '🎲 Thinking<span class="spinner"></span>';

        setTimeout(() => {
            const reason = this.getRandomReason();
            this.currentReason = reason;

            // Animate reason change
            this.reasonText.style.opacity = '0';
            this.reasonText.style.transform = 'translateY(20px)';

            setTimeout(() => {
                this.reasonText.textContent = reason;
                this.reasonText.style.opacity = '1';
                this.reasonText.style.transform = 'translateY(0)';
                this.updateHeartButton();

                // Remove loading state
                this.newReasonBtn.classList.remove('loading');
                this.newReasonBtn.innerHTML = '🎲 Tell Me Another One';
            }, 200);
        }, 500);
    }

    getRandomReason() {
        // If we've used all reasons, reset the used array
        if (this.usedReasons.length >= loveReasons.length) {
            this.usedReasons = [];
        }
        
        // Get available reasons
        const availableReasons = loveReasons.filter((reason, index) => 
            !this.usedReasons.includes(index)
        );
        
        // Pick a random available reason
        const randomIndex = Math.floor(Math.random() * availableReasons.length);
        const selectedReason = availableReasons[randomIndex];
        
        // Mark this reason as used
        const originalIndex = loveReasons.indexOf(selectedReason);
        this.usedReasons.push(originalIndex);
        
        return selectedReason;
    }

    updateHeartButton() {
        if (this.currentReason && this.favorites.includes(this.currentReason)) {
            this.heartBtn.textContent = '❤️';
            this.heartBtn.classList.add('favorited');
            this.heartBtn.title = 'Remove from favorites';
        } else {
            this.heartBtn.textContent = '🤍';
            this.heartBtn.classList.remove('favorited');
            this.heartBtn.title = 'Save to favorites';
        }
    }

    toggleFavorite() {
        if (!this.currentReason) return;
        
        const index = this.favorites.indexOf(this.currentReason);
        
        if (index > -1) {
            // Remove from favorites
            this.favorites.splice(index, 1);
            this.heartBtn.style.animation = 'heartBreak 0.3s ease';
        } else {
            // Add to favorites
            this.favorites.push(this.currentReason);
            this.heartBtn.style.animation = 'heartBeat 0.3s ease';
        }
        
        this.saveFavorites();
        this.updateHeartButton();
        this.displayFavorites();
        
        // Reset animation
        setTimeout(() => {
            this.heartBtn.style.animation = '';
        }, 300);
    }

    loadFavorites() {
        const saved = localStorage.getItem('love-favorites');
        return saved ? JSON.parse(saved) : [];
    }

    saveFavorites() {
        localStorage.setItem('love-favorites', JSON.stringify(this.favorites));
    }

    displayFavorites() {
        if (this.favorites.length === 0) {
            this.favoritesContainer.innerHTML = `
                <p style="color: var(--gray); font-style: italic; text-align: center;">
                    No favorites yet! Save the reasons that make your heart flutter 💕
                </p>
            `;
            this.favoritesGrid.innerHTML = '';
            this.clearFavoritesBtn.style.display = 'none';
        } else {
            this.favoritesContainer.innerHTML = `
                <p style="color: var(--dark-pink); text-align: center; margin-bottom: 1rem;">
                    You've saved ${this.favorites.length} reason${this.favorites.length !== 1 ? 's' : ''} that made you smile! 💖
                </p>
            `;
            
            this.favoritesGrid.innerHTML = '';
            this.favorites.forEach((reason, index) => {
                const favoriteItem = document.createElement('div');
                favoriteItem.className = 'favorite-item';
                favoriteItem.innerHTML = `
                    <button class="remove-favorite" data-index="${index}" title="Remove from favorites">×</button>
                    <div>${reason}</div>
                `;
                
                // Add remove functionality
                const removeBtn = favoriteItem.querySelector('.remove-favorite');
                removeBtn.addEventListener('click', () => this.removeFavorite(index));
                
                this.favoritesGrid.appendChild(favoriteItem);
            });
            
            this.clearFavoritesBtn.style.display = 'inline-block';
        }
    }

    removeFavorite(index) {
        this.favorites.splice(index, 1);
        this.saveFavorites();
        this.displayFavorites();
        this.updateHeartButton();
    }

    clearAllFavorites() {
        if (confirm('Are you sure you want to clear all your favorite reasons? This cannot be undone.')) {
            this.favorites = [];
            this.saveFavorites();
            this.displayFavorites();
            this.updateHeartButton();
        }
    }
}

// Initialize randomizer when page loads
document.addEventListener('DOMContentLoaded', function() {
    const randomizer = new LoveRandomizer();
    
    // Add heart animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes heartBeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }
        
        @keyframes heartBreak {
            0% { transform: scale(1); }
            25% { transform: scale(0.8) rotate(-10deg); }
            50% { transform: scale(0.9) rotate(10deg); }
            75% { transform: scale(0.85) rotate(-5deg); }
            100% { transform: scale(1) rotate(0deg); }
        }
        
        .mr-2 {
            margin-right: 1rem;
        }
    `;
    document.head.appendChild(style);
});
