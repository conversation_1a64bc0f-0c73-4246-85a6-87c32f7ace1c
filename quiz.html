<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Love Quiz - Happy Birthday Mannu</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💖</text></svg>">
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <div class="heart-badge">💖</div>
                <span>Happy Birthday Mannu</span>
            </a>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="quiz.html" class="active">Quiz</a></li>
                    <li><a href="randomizer.html">Love Notes</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="quiz-container">
            <!-- Quiz Header -->
            <div class="card mb-3" id="quiz-header">
                <h2>💕 The Love Quiz</h2>
                <p class="subtitle">Let's see how well you know my heart...</p>
                <div class="progress-container">
                    <div class="progress-bar" id="progress-bar" style="width: 0%"></div>
                </div>
                <div class="text-center">
                    <span id="progress-text">Question 1 of 12</span>
                </div>
            </div>

            <!-- Quiz Questions -->
            <div id="quiz-content">
                <div class="question-card" id="question-container">
                    <div class="question-number" id="question-number">Question 1</div>
                    <div class="question-text" id="question-text">Loading question...</div>
                    <div class="answers-grid" id="answers-container">
                        <!-- Answers will be populated by JavaScript -->
                    </div>
                </div>

                <div class="quiz-navigation">
                    <button class="btn btn-secondary" id="prev-btn" disabled>
                        ← Previous
                    </button>
                    <div class="quiz-info">
                        <span id="current-question">1</span> / <span id="total-questions">12</span>
                    </div>
                    <button class="btn btn-primary" id="next-btn" disabled>
                        Next →
                    </button>
                </div>
            </div>

            <!-- Results Screen -->
            <div id="results-screen" class="hidden">
                <div class="results-card">
                    <h2>🎉 Quiz Complete!</h2>
                    <div class="score-display" id="final-score">0/12</div>
                    <div class="reveal-message">
                        <strong>"You passed my heart's little test — every answer was a reminder of how much I love you."</strong>
                    </div>
                    
                    <div class="results-breakdown" id="results-breakdown">
                        <!-- Results breakdown will be populated by JavaScript -->
                    </div>
                    
                    <div class="results-actions mt-4">
                        <button class="btn btn-primary" id="retake-quiz">
                            🔄 Take Quiz Again
                        </button>
                        <a href="randomizer.html" class="btn btn-secondary">
                            💝 Read Love Notes
                        </a>
                        <a href="index.html" class="btn btn-secondary">
                            🏠 Back Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Confetti Canvas -->
    <canvas id="confetti-canvas"></canvas>

    <script src="js/quiz.js"></script>
</body>
</html>
