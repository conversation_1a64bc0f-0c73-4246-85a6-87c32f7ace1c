# Happy Birthday Mannu 🎉💖

A beautiful, interactive birthday website with a pastel pink theme created with love.

## Features

### 🏠 Landing Page (index.html)
- Welcoming birthday message
- Beautiful pastel pink design with Poppins font
- Navigation to quiz and love notes
- Responsive design for all devices
- Animated elements and micro-interactions

### 🎯 Interactive Quiz (quiz.html)
- 12 thoughtful multiple-choice questions
- Progress bar and question navigation
- Forward/backward navigation with answer persistence
- Final results screen with score breakdown
- Special reveal message: *"You passed my heart's little test — every answer was a reminder of how much I love you."*
- Confetti animation for scores ≥80%
- localStorage persistence for quiz progress

### 💝 Love Notes Randomizer (randomizer.html)
- 30+ heartfelt reasons with random generator
- Save favorites with heart button
- localStorage persistence for favorites
- View and manage saved favorites
- Clear all favorites option

## Design Features

### 🎨 Visual Design
- **Color Palette**: Pastel pink theme with soft gradients
- **Typography**: Poppins font family (Google Fonts)
- **Layout**: Responsive design (two-column on desktop, stacked on mobile)
- **Cards**: Rounded corners with soft shadows
- **Animations**: Smooth transitions and entrance effects

### ✨ Interactive Elements
- Button press animations with ripple effects
- Hover states and focus accessibility
- Progress bar with animated shine effect
- Heart button animations (beat/break)
- Confetti celebration on quiz completion
- Smooth page transitions

### 📱 Responsive Design
- Mobile-first approach
- Flexible grid layouts
- Touch-friendly button sizes
- Optimized typography scaling

## Technical Implementation

### 🛠️ Technologies Used
- **HTML5**: Semantic markup
- **CSS3**: Custom properties, flexbox, grid, animations
- **Vanilla JavaScript**: ES6+ classes and modern features
- **Canvas API**: Confetti animation
- **localStorage**: Data persistence

### 📁 File Structure
```
Birthday/
├── index.html          # Landing page
├── quiz.html           # Interactive quiz
├── randomizer.html     # Love notes randomizer
├── css/
│   └── styles.css      # Main stylesheet
├── js/
│   ├── quiz.js         # Quiz functionality
│   └── randomizer.js   # Randomizer functionality
└── README.md           # This file
```

## How to Use

1. **Open the website**: Simply open `index.html` in any modern web browser
2. **Navigate**: Use the header navigation or buttons to move between pages
3. **Take the Quiz**: Answer 12 questions about love and relationships
4. **Explore Love Notes**: Generate random reasons and save your favorites
5. **Enjoy**: All interactions are saved locally for a personalized experience

## Browser Compatibility

- ✅ Chrome/Edge (recommended)
- ✅ Firefox
- ✅ Safari
- ✅ Mobile browsers

## Accessibility Features

- High contrast mode support
- Reduced motion preferences respected
- Keyboard navigation support
- Focus indicators for all interactive elements
- Semantic HTML structure

## Local Storage Usage

The website uses localStorage to save:
- Quiz progress and answers
- Quiz completion results
- Favorite love notes

No data is sent to external servers - everything stays private on your device.

---

*Made with 💖 for Mannu's special day*
