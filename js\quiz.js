// Quiz Data and Functionality
const quizData = [
    {
        question: "What's my favorite way to spend a quiet evening?",
        answers: ["Watching movies together", "Reading books separately", "Playing video games", "Cooking dinner together"],
        correct: 0
    },
    {
        question: "Which of these makes me smile the most?",
        answers: ["Your laugh", "Surprise texts from you", "When you sing along to songs", "All of the above"],
        correct: 3
    },
    {
        question: "What's my love language?",
        answers: ["Words of affirmation", "Physical touch", "Quality time", "Acts of service"],
        correct: 2
    },
    {
        question: "If I could give you anything in the world, what would it be?",
        answers: ["A mansion", "All my time and attention", "Expensive jewelry", "A fancy car"],
        correct: 1
    },
    {
        question: "What do I think about most when we're apart?",
        answers: ["Work stuff", "What to have for dinner", "You and our memories", "Random thoughts"],
        correct: 2
    },
    {
        question: "Which moment with you do I treasure most?",
        answers: ["Our first date", "Every single moment", "Special occasions only", "Weekend adventures"],
        correct: 1
    },
    {
        question: "What's my favorite thing about your personality?",
        answers: ["Your sense of humor", "Your kindness", "Your intelligence", "Everything about you"],
        correct: 3
    },
    {
        question: "How do I feel when I see you first thing in the morning?",
        answers: ["Grateful", "Happy", "Like the luckiest person alive", "Sleepy"],
        correct: 2
    },
    {
        question: "What's my biggest dream for us?",
        answers: ["Traveling the world", "Growing old together", "Getting rich", "Becoming famous"],
        correct: 1
    },
    {
        question: "When I say 'I love you,' what do I really mean?",
        answers: ["I care about you", "You're important to me", "You're my everything", "It's just a phrase"],
        correct: 2
    },
    {
        question: "What makes you special to me?",
        answers: ["Your looks", "Your talents", "The way you make me feel complete", "Your achievements"],
        correct: 2
    },
    {
        question: "If I had to describe our love in one word, it would be:",
        answers: ["Perfect", "Infinite", "Beautiful", "Magical"],
        correct: 1
    }
];

class Quiz {
    constructor() {
        this.currentQuestion = 0;
        this.userAnswers = new Array(quizData.length).fill(null);
        this.score = 0;
        this.isCompleted = false;
        
        this.initializeElements();
        this.loadSavedProgress();
        this.displayQuestion();
        this.setupEventListeners();
    }

    initializeElements() {
        this.questionContainer = document.getElementById('question-container');
        this.questionNumber = document.getElementById('question-number');
        this.questionText = document.getElementById('question-text');
        this.answersContainer = document.getElementById('answers-container');
        this.progressBar = document.getElementById('progress-bar');
        this.progressText = document.getElementById('progress-text');
        this.currentQuestionSpan = document.getElementById('current-question');
        this.totalQuestionsSpan = document.getElementById('total-questions');
        this.prevBtn = document.getElementById('prev-btn');
        this.nextBtn = document.getElementById('next-btn');
        this.quizContent = document.getElementById('quiz-content');
        this.resultsScreen = document.getElementById('results-screen');
        this.finalScore = document.getElementById('final-score');
        this.resultsBreakdown = document.getElementById('results-breakdown');
        this.retakeBtn = document.getElementById('retake-quiz');
        
        this.totalQuestionsSpan.textContent = quizData.length;
    }

    setupEventListeners() {
        this.prevBtn.addEventListener('click', () => this.previousQuestion());
        this.nextBtn.addEventListener('click', () => this.nextQuestion());
        this.retakeBtn.addEventListener('click', () => this.restartQuiz());
    }

    loadSavedProgress() {
        const saved = localStorage.getItem('quiz-progress');
        if (saved) {
            const data = JSON.parse(saved);
            this.currentQuestion = data.currentQuestion || 0;
            this.userAnswers = data.userAnswers || new Array(quizData.length).fill(null);
        }
    }

    saveProgress() {
        localStorage.setItem('quiz-progress', JSON.stringify({
            currentQuestion: this.currentQuestion,
            userAnswers: this.userAnswers
        }));
    }

    displayQuestion() {
        const question = quizData[this.currentQuestion];
        
        // Update question info
        this.questionNumber.textContent = `Question ${this.currentQuestion + 1}`;
        this.questionText.textContent = question.question;
        this.currentQuestionSpan.textContent = this.currentQuestion + 1;
        
        // Update progress
        const progress = ((this.currentQuestion + 1) / quizData.length) * 100;
        this.progressBar.style.width = `${progress}%`;
        this.progressText.textContent = `Question ${this.currentQuestion + 1} of ${quizData.length}`;
        
        // Create answer options
        this.answersContainer.innerHTML = '';
        question.answers.forEach((answer, index) => {
            const answerElement = document.createElement('div');
            answerElement.className = 'answer-option';
            answerElement.textContent = answer;
            answerElement.dataset.index = index;
            
            // Check if this answer was previously selected
            if (this.userAnswers[this.currentQuestion] === index) {
                answerElement.classList.add('selected');
            }
            
            answerElement.addEventListener('click', () => this.selectAnswer(index));
            this.answersContainer.appendChild(answerElement);
        });
        
        // Update navigation buttons
        this.prevBtn.disabled = this.currentQuestion === 0;
        this.updateNextButton();
        
        // Add entrance animation
        this.questionContainer.style.animation = 'none';
        this.questionContainer.offsetHeight; // Trigger reflow
        this.questionContainer.style.animation = 'cardEntrance 0.4s ease-out';
    }

    selectAnswer(answerIndex) {
        // Remove previous selection
        this.answersContainer.querySelectorAll('.answer-option').forEach(option => {
            option.classList.remove('selected');
        });
        
        // Add selection to clicked answer
        const selectedOption = this.answersContainer.querySelector(`[data-index="${answerIndex}"]`);
        selectedOption.classList.add('selected');
        
        // Save answer
        this.userAnswers[this.currentQuestion] = answerIndex;
        this.saveProgress();
        this.updateNextButton();
    }

    updateNextButton() {
        const hasAnswer = this.userAnswers[this.currentQuestion] !== null;
        this.nextBtn.disabled = !hasAnswer;
        
        if (this.currentQuestion === quizData.length - 1) {
            this.nextBtn.textContent = hasAnswer ? 'Finish Quiz 🎉' : 'Select Answer';
        } else {
            this.nextBtn.textContent = 'Next →';
        }
    }

    previousQuestion() {
        if (this.currentQuestion > 0) {
            this.currentQuestion--;
            this.displayQuestion();
        }
    }

    nextQuestion() {
        if (this.userAnswers[this.currentQuestion] === null) return;
        
        if (this.currentQuestion < quizData.length - 1) {
            this.currentQuestion++;
            this.displayQuestion();
        } else {
            this.completeQuiz();
        }
    }

    completeQuiz() {
        this.calculateScore();
        this.showResults();
        this.saveQuizResults();
        
        // Show confetti if score >= 80%
        if (this.score >= Math.ceil(quizData.length * 0.8)) {
            this.showConfetti();
        }
    }

    calculateScore() {
        this.score = 0;
        this.userAnswers.forEach((answer, index) => {
            if (answer === quizData[index].correct) {
                this.score++;
            }
        });
    }

    showResults() {
        this.quizContent.classList.add('hidden');
        this.resultsScreen.classList.remove('hidden');
        
        // Display score
        this.finalScore.textContent = `${this.score}/${quizData.length}`;
        
        // Create results breakdown
        this.createResultsBreakdown();
        
        // Add entrance animation
        this.resultsScreen.style.animation = 'cardEntrance 0.6s ease-out';
    }

    createResultsBreakdown() {
        const breakdown = document.createElement('div');
        breakdown.className = 'quiz-breakdown';
        breakdown.innerHTML = `
            <h3 style="margin-bottom: 1rem; color: var(--dark-pink);">📊 Your Results</h3>
            <div style="display: grid; gap: 0.5rem; text-align: left; max-width: 400px; margin: 0 auto;">
                <div style="display: flex; justify-content: space-between; padding: 0.5rem; background: var(--success); border-radius: 8px;">
                    <span>✅ Correct Answers:</span>
                    <strong>${this.score}</strong>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 0.5rem; background: var(--soft-pink); border-radius: 8px;">
                    <span>📝 Total Questions:</span>
                    <strong>${quizData.length}</strong>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 0.5rem; background: var(--pale-pink); border-radius: 8px;">
                    <span>📈 Success Rate:</span>
                    <strong>${Math.round((this.score / quizData.length) * 100)}%</strong>
                </div>
            </div>
        `;
        
        this.resultsBreakdown.innerHTML = '';
        this.resultsBreakdown.appendChild(breakdown);
    }

    saveQuizResults() {
        const results = {
            score: this.score,
            totalQuestions: quizData.length,
            percentage: Math.round((this.score / quizData.length) * 100),
            completedAt: new Date().toISOString(),
            answers: this.userAnswers
        };
        
        localStorage.setItem('quiz-results', JSON.stringify(results));
        localStorage.removeItem('quiz-progress'); // Clear progress since quiz is complete
    }

    restartQuiz() {
        this.currentQuestion = 0;
        this.userAnswers = new Array(quizData.length).fill(null);
        this.score = 0;
        this.isCompleted = false;
        
        localStorage.removeItem('quiz-progress');
        localStorage.removeItem('quiz-results');
        
        this.resultsScreen.classList.add('hidden');
        this.quizContent.classList.remove('hidden');
        this.displayQuestion();
    }

    showConfetti() {
        const canvas = document.getElementById('confetti-canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        const confetti = [];
        const colors = ['#FFB6C1', '#FFC0CB', '#FF69B4', '#DB7093', '#FFE4E1'];
        
        // Create confetti particles
        for (let i = 0; i < 100; i++) {
            confetti.push({
                x: Math.random() * canvas.width,
                y: -10,
                vx: (Math.random() - 0.5) * 4,
                vy: Math.random() * 3 + 2,
                color: colors[Math.floor(Math.random() * colors.length)],
                size: Math.random() * 8 + 4,
                rotation: Math.random() * 360,
                rotationSpeed: (Math.random() - 0.5) * 10
            });
        }
        
        function animateConfetti() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            confetti.forEach((particle, index) => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.rotation += particle.rotationSpeed;
                
                ctx.save();
                ctx.translate(particle.x, particle.y);
                ctx.rotate(particle.rotation * Math.PI / 180);
                ctx.fillStyle = particle.color;
                ctx.fillRect(-particle.size/2, -particle.size/2, particle.size, particle.size);
                ctx.restore();
                
                // Remove particles that are off screen
                if (particle.y > canvas.height + 10) {
                    confetti.splice(index, 1);
                }
            });
            
            if (confetti.length > 0) {
                requestAnimationFrame(animateConfetti);
            } else {
                canvas.style.display = 'none';
            }
        }
        
        canvas.style.display = 'block';
        animateConfetti();
        
        // Hide confetti after 5 seconds
        setTimeout(() => {
            canvas.style.display = 'none';
        }, 5000);
    }
}

// Initialize quiz when page loads
document.addEventListener('DOMContentLoaded', function() {
    new Quiz();
});
